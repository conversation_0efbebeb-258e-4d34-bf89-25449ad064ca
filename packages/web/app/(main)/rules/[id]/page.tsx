"use client";

import { useParams } from "next/navigation";
import { <PERSON>, Badge, But<PERSON> } from "@radix-ui/themes";
import { Copy, ArrowLeft, Download, Share, ExternalLink } from "lucide-react";
import { toast } from "sonner";
import Link from "next/link";
import { useRule } from "@/hooks/use-rule-queries";

// Force dynamic rendering to avoid build-time database issues
export const dynamic = 'force-dynamic';

export default function RulePage() {
  const params = useParams();
  const ruleId = params.id as string;
  
  // Use the query hook instead of manual fetch
  const { 
    data: rule, 
    isLoading, 
    error, 
    isError 
  } = useRule(ruleId);

  const handleCopyContent = async () => {
    if (!rule) return;
    try {
      await navigator.clipboard.writeText(rule.content);
      toast.success("Rule content copied to clipboard");
    } catch (err) {
      toast.error("Failed to copy content");
    }
  };

  const handleCopyLink = async () => {
    try {
      const url = `${window.location.origin}/r/${ruleId}`;
      await navigator.clipboard.writeText(url);
      toast.success("Rule link copied to clipboard");
    } catch (err) {
      toast.error("Failed to copy link");
    }
  };

  const handleDownload = () => {
    if (!rule) return;
    const blob = new Blob([rule.content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${rule.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast.success("Rule downloaded");
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="container max-w-4xl py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Loading Rule...</h1>
          <p className="text-muted-foreground">Rule ID: {ruleId}</p>
          <div className="mt-4">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mx-auto"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (isError || !rule) {
    const errorMessage = error instanceof Error 
      ? error.message 
      : "Rule not found";
    
    return (
      <div className="container max-w-4xl py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4 text-red-600">Error</h1>
          <p className="text-muted-foreground mb-4">{errorMessage}</p>
          <Button variant="outline" asChild>
            <Link href="/rules">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Rules
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container max-w-4xl py-8 space-y-6">
      {/* Header */}
      <div className="flex items-start justify-between">
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Button variant="ghost" size="1" asChild>
              <Link href="/rules">
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <span>Rules</span>
          </div>
          <h1 className="text-3xl font-bold">{rule.title}</h1>
          {rule.description && (
            <p className="text-lg text-muted-foreground">{rule.description}</p>
          )}
        </div>
        
        {/* Action Buttons */}
        <div className="flex items-center gap-2">
          <Button variant="outline" size="2" onClick={handleCopyContent}>
            <Copy className="mr-2 h-4 w-4" />
            Copy
          </Button>
          <Button variant="outline" size="2" onClick={handleCopyLink}>
            <Share className="mr-2 h-4 w-4" />
            Share
          </Button>
          <Button variant="outline" size="2" onClick={handleDownload}>
            <Download className="mr-2 h-4 w-4" />
            Download
          </Button>
        </div>
      </div>

      {/* Metadata */}
      <Card className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">IDE:</span>
            <Badge variant="outline">{rule.ideType}</Badge>
          </div>
          
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Visibility:</span>
            <Badge variant={rule.visibility === 'PUBLIC' ? 'solid' : 'outline'}>
              {rule.visibility}
            </Badge>
          </div>
          
          {rule.user && (
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">Author:</span>
              <span className="text-sm">{rule.user.name || rule.user.email}</span>
            </div>
          )}
          
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Created:</span>
            <span className="text-sm">
              {new Date(rule.createdAt).toLocaleDateString()}
            </span>
          </div>
        </div>

        {/* Tags */}
        {rule.tags && rule.tags.length > 0 && (
          <div className="mt-4 pt-4 border-t">
            <div className="flex items-center gap-2 flex-wrap">
              <span className="text-sm font-medium">Tags:</span>
              {rule.tags.map((tagRelation) => (
                <Badge 
                  key={tagRelation.tag.id} 
                  variant="soft"
                  style={{ 
                    backgroundColor: tagRelation.tag.color + '20', 
                    color: tagRelation.tag.color 
                  }}
                >
                  {tagRelation.tag.name}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </Card>

      {/* Rule Content */}
      <Card className="p-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Rule Content</h2>
            <Button variant="ghost" size="1" onClick={handleCopyContent}>
              <Copy className="h-4 w-4" />
            </Button>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg border">
            <pre className="whitespace-pre-wrap text-sm font-mono overflow-x-auto">
              {rule.content}
            </pre>
          </div>
        </div>
      </Card>

      {/* Raw Data Link */}
      {rule.visibility === 'PUBLIC' && (
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium">Raw Data Access</h3>
              <p className="text-sm text-muted-foreground">
                Access the raw rule content directly via API
              </p>
            </div>
            <Button variant="outline" size="2" asChild>
              <Link href={`/api/rules/raw?id=${rule.id}`} target="_blank">
                <ExternalLink className="mr-2 h-4 w-4" />
                Raw Data
              </Link>
            </Button>
          </div>
        </Card>
      )}
    </div>
  );
}

