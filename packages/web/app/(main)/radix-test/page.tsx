"use client";

import { <PERSON><PERSON>, <PERSON>, Text, Heading, Box, Flex } from "@radix-ui/themes";

export default function RadixTestPage() {
  return (
    <Box p="4">
      <Heading size="6" mb="4">Radix UI Theme Test</Heading>
      
      <Flex direction="column" gap="4" style={{ maxWidth: "600px" }}>
        {/* Test basic components */}
        <Card>
          <Text>This is a Radix UI Card component</Text>
        </Card>
        
        {/* Test buttons */}
        <Flex gap="2">
          <Button>Default <PERSON><PERSON></Button>
          <Button variant="soft">Soft Button</Button>
          <Button variant="outline">Outline Button</Button>
        </Flex>
        
        {/* Test custom CSS with Radix variables */}
        <div 
          className="bg-background border-default"
          style={{
            padding: "16px",
            borderWidth: "1px",
            borderStyle: "solid",
            borderRadius: "8px",
            backgroundColor: "var(--color-panel-solid)",
            color: "var(--gray-12)",
            border: "1px solid var(--gray-6)"
          }}
        >
          <Text>This div uses Radix CSS variables directly</Text>
        </div>
        
        {/* Test color tokens */}
        <Flex gap="2">
          <div style={{ 
            width: "50px", 
            height: "50px", 
            backgroundColor: "var(--accent-9)",
            borderRadius: "4px"
          }} />
          <div style={{ 
            width: "50px", 
            height: "50px", 
            backgroundColor: "var(--gray-9)",
            borderRadius: "4px"
          }} />
          <div style={{ 
            width: "50px", 
            height: "50px", 
            backgroundColor: "var(--color-background)",
            border: "1px solid var(--gray-6)",
            borderRadius: "4px"
          }} />
        </Flex>
      </Flex>
    </Box>
  );
}
