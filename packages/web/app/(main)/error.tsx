'use client';

import { useEffect } from 'react';
import { But<PERSON>, Container, Heading, Text, Flex } from '@radix-ui/themes';
import { RefreshCw, ArrowLeft } from 'lucide-react';
import Link from 'next/link';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Route error:', error);
  }, [error]);

  return (
    <Container size="2" className="min-h-screen flex items-center justify-center">
      <Flex direction="column" align="center" gap="6" className="text-center">
        <Heading size="8" className="text-5xl font-bold text-red-600">
          Oops! Something went wrong
        </Heading>
        <Text size="4" color="gray" className="max-w-md">
          We encountered an unexpected error while loading this page. Please try again or go back to the previous page.
        </Text>
        {error.message && (
          <Text size="3" color="gray" className="font-mono bg-gray-100 p-2 rounded">
            {error.message}
          </Text>
        )}
        {error.digest && (
          <Text size="2" color="gray" className="font-mono">
            Error ID: {error.digest}
          </Text>
        )}
        <Flex gap="3" className="mt-4">
          <Button variant="solid" onClick={reset}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Try again
          </Button>
          <Button variant="outline" asChild>
            <Link href="/">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Go Home
            </Link>
          </Button>
        </Flex>
      </Flex>
    </Container>
  );
}
