import { Metadata } from "next";

// Force dynamic rendering to avoid build-time database issues
export const dynamic = 'force-dynamic';

// Static metadata for the page
export const metadata: Metadata = {
  title: "OnlyRules - AI Rule Sets",
  description: "Browse AI rule sets for developers - collections of rules for quick setup"
};

export default async function RulesetsPage() {
  // Temporary placeholder during build - this will be replaced with proper implementation
  return (
    <div className="container max-w-6xl py-8">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold mb-4">AI Rule Sets</h1>
        <p className="text-muted-foreground text-lg">
          Collections of AI rules for quick setup and deployment
        </p>
      </div>
      
      <div className="text-center">
        <p className="text-sm text-muted-foreground">
          This page is being built. Please check back soon.
        </p>
      </div>
    </div>
  );
}