import React from 'react';
import { <PERSON>ada<PERSON> } from 'next';
import IDEPageTemplate from '@/components/ide-page-template';
import { Spark<PERSON>, Layers, Cpu, Link } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Augment Code Integration - Enhanced AI Development',
  description: 'Complete guide to integrating OnlyRules with Augment Code. Learn AI-enhanced coding, setup instructions, and advanced features for better development.',
  keywords: 'Augment Code, AI development, OnlyRules Augment, code enhancement, AI IDE, Augment setup, smart coding, AI integration',
  alternates: {
    canonical: '/ides/augment',
  },
  openGraph: {
    title: 'Augment Code Integration - OnlyRules Guide',
    description: 'Enhance your coding experience with Augment Code and OnlyRules custom prompts.',
    images: ['/images/augment-integration-og.png'],
  },
};

const augmentData = {
  ide: {
    name: 'Augment Code',
    icon: '🟣',
    color: 'bg-purple-500',
    description: 'Enhance your coding experience with advanced AI assistance',
    website: 'https://augmentcode.com',
    version: '2.0+',
  },
  installation: {
    steps: [
      {
        title: 'Download Augment',
        description: 'Get Augment Code from the official website for your operating system.',
        command: 'https://augmentcode.com/download',
        note: 'Augment is available for Windows, macOS, and Linux with native performance.',
      },
      {
        title: 'Install and Launch',
        description: 'Run the installer and launch Augment. The setup wizard will guide you through initial configuration.',
      },
      {
        title: 'Connect AI Provider',
        description: 'Configure your preferred AI model provider in Augment settings.',
        note: 'Augment supports multiple AI providers including OpenAI, Anthropic, and local models.',
      },
      {
        title: 'Customize Workspace',
        description: 'Set up your workspace preferences and keyboard shortcuts for optimal workflow.',
        command: 'Preferences → Workspace → AI Assistant',
      },
    ],
  },
  integration: {
    steps: [
      {
        title: 'Find Augment Templates',
        description: 'Browse OnlyRules for Augment-specific AI templates and patterns.',
        code: 'https://onlyrules.app/templates?ide=AUGMENT',
      },
      {
        title: 'Create Augment Config',
        description: 'Set up an .augment directory with configuration files.',
        code: `mkdir .augment
touch .augment/prompts.yaml
touch .augment/context.md`,
      },
      {
        title: 'Configure AI Prompts',
        description: 'Define your custom prompts and AI behaviors in YAML format.',
        code: `# .augment/prompts.yaml

version: 1.0
prompts:
  - name: "Component Generator"
    trigger: "/component"
    template: |
      Create a React component with:
      - TypeScript interfaces
      - Proper prop validation
      - Error boundaries
      - Accessibility features
      - Unit tests
      
  - name: "API Endpoint"
    trigger: "/api"
    template: |
      Generate REST API endpoint with:
      - Input validation
      - Error handling
      - Authentication check
      - Rate limiting
      - OpenAPI documentation
      
  - name: "Database Migration"
    trigger: "/migration"
    template: |
      Create database migration:
      - Up and down methods
      - Transaction safety
      - Rollback capability
      - Data integrity checks`,
      },
      {
        title: 'Set Project Context',
        description: 'Provide Augment with project-specific context and guidelines.',
        code: `# .augment/context.md

## Project Architecture
- Frontend: React 18 with TypeScript
- Backend: Node.js with Express
- Database: PostgreSQL with Prisma
- Testing: Jest and React Testing Library

## Coding Standards
- Functional components only
- Custom hooks for logic
- Comprehensive error handling
- 90% test coverage minimum

## AI Guidelines
- Follow existing patterns
- Prioritize readability
- Include documentation
- Consider performance`,
      },
    ],
  },
  features: [
    {
      title: 'Smart Suggestions',
      description: 'Context-aware code completions that understand your project',
      icon: 'sparkles',
    },
    {
      title: 'Layer Analysis',
      description: 'Understands application layers and maintains separation of concerns',
      icon: 'layers',
    },
    {
      title: 'Performance Optimization',
      description: 'AI-driven performance suggestions and optimizations',
      icon: 'cpu',
    },
  ],
  examples: [
    {
      title: 'Smart Component Refactoring',
      description: 'Intelligently refactor components with AI assistance',
      prompt: `/refactor
This component is too complex. Please:
- Extract custom hooks for data fetching
- Separate presentation from logic
- Add proper TypeScript types
- Implement error boundaries
- Optimize re-renders`,
      result: 'Cleanly refactored component with separated concerns and optimizations',
    },
    {
      title: 'API Layer Generation',
      description: 'Generate complete API layer with best practices',
      prompt: `/generate-api users
Create a complete users API with:
- CRUD operations
- Pagination and filtering
- Authentication middleware
- Input validation with Zod
- Swagger documentation
- Rate limiting`,
      result: 'Full API layer with all endpoints, middleware, and documentation',
    },
    {
      title: 'Test Suite Creation',
      description: 'Generate comprehensive test suites automatically',
      prompt: `/test-suite UserService
Generate tests that:
- Cover all public methods
- Test error scenarios
- Mock external dependencies
- Include integration tests
- Add performance benchmarks`,
      result: 'Complete test suite with unit, integration, and performance tests',
    },
  ],
  tips: [
    'Use Augment\'s layer-aware features to maintain clean architecture',
    'Create custom triggers for frequently used code patterns',
    'Leverage the context file to maintain consistency across your team',
    'Use Augment\'s performance analyzer before deploying to production',
    'Combine multiple prompts in sequences for complex operations',
    'Export and share your prompt configurations with team members',
    'Use Augment\'s diff view to review AI suggestions before applying',
    'Set up project-specific shortcuts for common AI operations',
    'Take advantage of Augment\'s learning feature to improve suggestions over time',
    'Use OnlyRules templates as a starting point and customize for your needs',
  ],
};

export default function AugmentPage() {
  // Convert string identifiers to JSX icons
  const iconMap = {
    sparkles: <Sparkles className="h-5 w-5" />,
    layers: <Layers className="h-5 w-5" />,
    cpu: <Cpu className="h-5 w-5" />,
  };

  const featuresWithIcons = augmentData.features.map(feature => ({
    ...feature,
    icon: iconMap[feature.icon as keyof typeof iconMap],
  }));

  return <IDEPageTemplate {...augmentData} features={featuresWithIcons} />;
}