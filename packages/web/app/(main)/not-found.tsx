import Link from 'next/link';
import { <PERSON><PERSON>, Container, Heading, Text, Flex } from '@radix-ui/themes';
import { ArrowLeft, Home } from 'lucide-react';

export default function NotFound() {
  return (
    <Container size="2" className="min-h-screen flex items-center justify-center">
      <Flex direction="column" align="center" gap="6" className="text-center">
        <Heading size="9" className="text-6xl font-bold">
          404
        </Heading>
        <Heading size="6">
          Page Not Found
        </Heading>
        <Text size="4" color="gray" className="max-w-md">
          Sorry, we couldn't find the page you're looking for. The page might have been moved, deleted, or you might have entered the wrong URL.
        </Text>
        <Flex gap="3" className="mt-4">
          <Button variant="solid" asChild>
            <Link href="/">
              <Home className="mr-2 h-4 w-4" />
              Go Home
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/rules">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Browse Rules
            </Link>
          </Button>
        </Flex>
      </Flex>
    </Container>
  );
}
