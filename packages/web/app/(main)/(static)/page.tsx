import Link from "next/link";
import { Box, Container, Flex, Heading, Text, Button } from "@radix-ui/themes";

// Force dynamic rendering to avoid build issues
export const dynamic = 'force-dynamic';

export default function HomePage() {
  return (
    <>
      {/* <StaticNavbar /> */}
      <Box
        style={{ minHeight: '100vh' }}
        className="flex items-center"
      >
        <Container size="3" px={{ initial: '4', xs: '6' }}>
          <Flex
            direction="column"
            align="center"
            gap={{ initial: '6', xs: '8', md: '9' }}
            py={{ initial: '8', xs: '9', md: '9' }}
            className="text-center"
          >
            {/* Hero Title */}
            <Heading
              size={{ initial: '8', xs: '9', md: '9' }}
              weight="bold"
              className="tracking-tight"
            >
              OnlyRules
            </Heading>

            {/* Hero Description */}
            <Text
              size={{ initial: '4', xs: '5', md: '5' }}
              color="gray"
              className="max-w-2xl"
            >
              AI Prompt Management Platform
            </Text>

            {/* Action Buttons */}
            <Flex
              direction={{ initial: 'column', sm: 'row' }}
              gap={{ initial: '3', xs: '4' }}
              mt={{ initial: '4', xs: '6' }}
              width={{ initial: '100%', xs: 'auto' }}
            >
              <Button
                size={{ initial: '3', xs: '3' }}
                variant="solid"
                asChild
              >
                <Link href="/demo">
                  View Demo
                </Link>
              </Button>

              <Button
                size={{ initial: '3', xs: '3' }}
                variant="soft"
                asChild
              >
                <Link href="/auth/signin">
                  Get Started
                </Link>
              </Button>

              <Button
                size={{ initial: '3', xs: '3' }}
                variant="outline"
                asChild
              >
                <Link href="/templates">
                  Browse Templates
                </Link>
              </Button>
            </Flex>
          </Flex>
        </Container>
      </Box>
    </>
  );
}
