
@import "../index.css";

/* @import 'tailwindcss'; */
/* @import 'fumadocs-ui/css/neutral.css';
@import 'fumadocs-ui/css/preset.css'; */

/* Working docs styling - simplified and targeted */

/* Import fumadocs-ui styles */
/* Import fumadocs-ui styles */
/* @import 'fumadocs-ui/css/neutral.css';
@import 'fumadocs-ui/css/preset.css'; */

/* Force Tailwind CSS styling on fumadocs-ui elements */
.fumadocs-doc,
.fumadocs-doc *,
.fumadocs-doc > * {
  color: hsl(var(--foreground));
  font-family: inherit;
}

/* Target the actual fumadocs-ui content */
.fumadocs-doc {
  padding: 2rem !important;
  max-width: none !important;
}

/* Typography with Tailwind scale */
.fumadocs-doc h1,
.fumadocs-doc h1 * {
  color: hsl(var(--foreground)) !important;
  font-size: 2.25rem !important;
  font-weight: 700 !important;
  margin: 2rem 0 1rem 0 !important;
  line-height: 1.2 !important;
}

.fumadocs-doc h2,
.fumadocs-doc h2 * {
  color: hsl(var(--foreground)) !important;
  font-size: 1.875rem !important;
  font-weight: 600 !important;
  margin: 1.5rem 0 0.75rem 0 !important;
  line-height: 1.3 !important;
}

.fumadocs-doc h3,
.fumadocs-doc h3 * {
  color: hsl(var(--foreground)) !important;
  font-size: 1.5rem !important;
  font-weight: 600 !important;
  margin: 1.25rem 0 0.5rem 0 !important;
  line-height: 1.4 !important;
}

.fumadocs-doc p,
.fumadocs-doc p * {
  color: hsl(var(--muted-foreground)) !important;
  line-height: 1.7 !important;
  margin-bottom: 1rem !important;
}

.fumadocs-doc a,
.fumadocs-doc a * {
  color: hsl(var(--primary)) !important;
  text-decoration: none !important;
}

.fumadocs-doc a:hover,
.fumadocs-doc a:hover * {
  text-decoration: underline !important;
}

/* Lists */
.fumadocs-doc ul,
.fumadocs-doc ol {
  margin: 1rem 0 !important;
  padding-left: 1.5rem !important;
}

.fumadocs-doc li,
.fumadocs-doc li * {
  color: hsl(var(--muted-foreground)) !important;
  margin-bottom: 0.5rem !important;
  line-height: 1.6 !important;
}

.fumadocs-doc li strong,
.fumadocs-doc li strong * {
  color: hsl(var(--foreground)) !important;
  font-weight: 600 !important;
}

/* Code blocks */
.fumadocs-doc code,
.fumadocs-doc code * {
  background-color: hsl(var(--muted)) !important;
  color: hsl(var(--foreground)) !important;
  padding: 0.125rem 0.375rem !important;
  border-radius: 0.25rem !important;
  font-size: 0.875rem !important;
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace !important;
}

.fumadocs-doc pre,
.fumadocs-doc pre * {
  background-color: hsl(var(--muted)) !important;
  border: 1px solid hsl(var(--border)) !important;
  border-radius: 0.5rem !important;
  padding: 1rem !important;
  overflow-x: auto !important;
  margin: 1rem 0 !important;
}

.fumadocs-doc pre code,
.fumadocs-doc pre code * {
  background-color: transparent !important;
  padding: 0 !important;
}

/* Blockquotes */
.fumadocs-doc blockquote,
.fumadocs-doc blockquote * {
  border-left: 4px solid hsl(var(--border)) !important;
  padding-left: 1rem !important;
  margin: 1.5rem 0 !important;
  color: hsl(var(--muted-foreground)) !important;
  font-style: italic !important;
}

/* Tables */
.fumadocs-doc table,
.fumadocs-doc table * {
  width: 100% !important;
  border-collapse: collapse !important;
  border: 1px solid hsl(var(--border)) !important;
  margin: 1rem 0 !important;
}

.fumadocs-doc th,
.fumadocs-doc th *,
.fumadocs-doc td,
.fumadocs-doc td * {
  border: 1px solid hsl(var(--border)) !important;
  padding: 0.75rem !important;
  text-align: left !important;
}

.fumadocs-doc th,
.fumadocs-doc th * {
  background-color: hsl(var(--muted)) !important;
  font-weight: 600 !important;
  color: hsl(var(--foreground)) !important;
}

/* Responsive */
@media (max-width: 768px) {
  .fumadocs-doc {
    padding: 1rem !important;
  }
  
  .fumadocs-doc h1,
  .fumadocs-doc h1 * {
    font-size: 1.875rem !important;
  }
  
  .fumadocs-doc h2,
  .fumadocs-doc h2 * {
    font-size: 1.5rem !important;
  }
  
  .fumadocs-doc h3,
  .fumadocs-doc h3 * {
    font-size: 1.25rem !important;
  }
}

/* Ensure all text uses Tailwind color system */
.fumadocs-doc .text-muted-foreground,
.fumadocs-doc .text-foreground,
.fumadocs-doc .text-primary {
  color: inherit !important;
}

/* Force color inheritance */
.fumadocs-doc * {
  color: inherit !important;
}

