"use client";

import * as React from "react";
import { ThemeProvider as NextThemesProvider } from "next-themes";
import { type ThemeProviderProps } from "next-themes";
import { Theme } from "@radix-ui/themes";
import { useTheme } from "next-themes";

function RadixThemeWrapper({ children }: { children: React.ReactNode }) {
  const { resolvedTheme } = useTheme();
  const [mounted, setMounted] = React.useState(false);

  

  // Safely get theme with fallback
  let theme = "dark";
  let systemTheme = "dark";

  try {
    const themeHook = useTheme();
    theme = themeHook.theme || "dark";
    systemTheme = themeHook.systemTheme || "dark";
  } catch (error) {
    // Fallback during SSR or when context is not available
    console.warn("Theme context not available, using default theme");
  }

  React.useEffect(() => {
    setMounted(true);
  }, []);

  // Use light theme as default and during hydration
  const appearance = mounted ? (resolvedTheme as "light" | "dark") || "light" : "light";

  // Apply theme class to document element for global CSS variable access
  React.useEffect(() => {
    if (typeof document !== 'undefined') {
      // Update the document class when theme changes
      const currentClass = `radix-themes ${appearance}`;
      document.documentElement.className = currentClass;
    }
  }, [appearance]);

  return (
    <Theme
      accentColor="blue"
      grayColor="slate"
      radius="medium"
      scaling="100%"
      appearance={appearance}
      panelBackground="translucent"
      hasBackground={true}
    >
      {children}
    </Theme>
  );
}

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  return (
    <NextThemesProvider
      defaultTheme="light"
      enableSystem={true}
      attribute="class"
      disableTransitionOnChange={false}
      {...props}
    >
      <RadixThemeWrapper>{children}</RadixThemeWrapper>
    </NextThemesProvider>
  );
}
