import Link from "next/link";
import { Box, Flex, Container, Text, Button } from "@radix-ui/themes";
import { Code, Github as GitHubIcon } from "lucide-react";

export function StaticNavbar() {
  return (
    <Box
      asChild
      style={{
        position: 'sticky',
        top: 0,
        zIndex: 50,
        width: '100%',
        borderBottom: '1px solid var(--gray-6)',
        backgroundColor: 'var(--color-panel-translucent)',
        backdropFilter: 'blur(8px)',
      }}
    >
      <nav>
        <Container size="4">
          <Flex
            align="center"
            justify="between"
            height="64px"
            px="4"
          >
            {/* Logo and Navigation */}
            <Flex align="center" gap="8">
              <Link href="/">
                <Flex align="center" gap="2">
                  <Code size={20} className="text-[var(--accent-9)]" />
                  <Text
                    size="5"
                    weight="bold"
                    color="gray"
                    highContrast
                  >
                    OnlyRules
                  </Text>
                </Flex>
              </Link>

              {/* Desktop Navigation */}
              <Box display={{ initial: "none", md: "block" }}>
                <Flex align="center" gap="6">
                  <Link href="/templates">
                    <Text
                      size="2"
                      weight="medium"
                      color="gray"
                      className="transition-colors hover:text-[var(--accent-9)]"
                    >
                      Templates
                    </Text>
                  </Link>
                  <Link href="/tutorials">
                    <Text
                      size="2"
                      weight="medium"
                      color="gray"
                      className="transition-colors hover:text-[var(--accent-9)]"
                    >
                      Tutorials
                    </Text>
                  </Link>
                  <Link href="/dashboard">
                    <Text
                      size="2"
                      weight="medium"
                      color="gray"
                      className="transition-colors hover:text-[var(--accent-9)]"
                    >
                      Dashboard
                    </Text>
                  </Link>
                </Flex>
              </Box>
            </Flex>

            {/* Right Side Actions */}
            <Flex align="center" gap="4">
              <Link
                href="https://github.com/ranglang/onlyrules"
                target="_blank"
                rel="noopener noreferrer"
              >
                <Flex align="center" gap="2">
                  <GitHubIcon size={16} />
                  <Text
                    size="2"
                    color="gray"
                    className="transition-colors hover:text-[var(--accent-9)]"
                  >
                    GitHub
                  </Text>
                </Flex>
              </Link>

              <Button size="2" asChild>
                <Link href="/auth/signin">
                  <Text size="2" weight="medium">Sign In</Text>
                </Link>
              </Button>
            </Flex>
          </Flex>
        </Container>
      </nav>
    </Box>
  );
}
