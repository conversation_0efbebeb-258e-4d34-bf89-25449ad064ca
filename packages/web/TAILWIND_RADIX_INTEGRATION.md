# Tailwind CSS + Radix UI Theme3 Integration Guide

## Overview
This guide ensures seamless integration between Tailwind CSS and Radix UI Theme3 in your Next.js project.

## Current Setup Status ✅
- ✅ Tailwind CSS v4.1.11 installed
- ✅ @radix-ui/themes v3.2.1 installed
- ✅ Proper CSS import order configured
- ✅ CSS variables for design tokens
- ✅ Responsive design tokens

## Integration Architecture

### 1. CSS Import Order (Fixed)
```css
/* globals.css */
@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

/* Radix UI Theme - imported after Tailwind base */
@import "@radix-ui/themes/styles.css";
```

### 2. Design Token Integration
Your project uses CSS variables that work with both Tailwind and Radix:

```css
:root {
  /* Radix UI color tokens */
  --background: 0 0% 100%;
  --foreground: 240 10% 3.9%;
  --primary: 217 91% 60%;
  --secondary: 240 4.8% 95.9%;
  --accent: 240 4.8% 95.9%;
  --destructive: 0 84.2% 60.2%;
  --border: 240 5.9% 90%;
  --input: 240 5.9% 90%;
  --ring: 217 91% 60%;
  --radius: 0.5rem;
}
```

### 3. Tailwind Configuration
Your `tailwind.config.ts` is properly configured with:
- CSS variable color system
- Responsive breakpoints
- Animation keyframes for Radix components
- Typography plugin

## Usage Patterns

### Pattern 1: Radix Components with Tailwind Styling
```tsx
import { Button } from '@radix-ui/themes';
import { cn } from '@/lib/utils';

// Use Radix components with Tailwind classes
<Button 
  className="bg-primary text-primary-foreground hover:bg-primary/90"
  size="3"
>
  Click me
</Button>
```

### Pattern 2: Custom Components
```tsx
// components/ui/button.tsx
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';

const buttonVariants = cva(
  'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        outline: 'border border-input hover:bg-accent hover:text-accent-foreground',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'underline-offset-4 hover:underline text-primary',
      },
      size: {
        default: 'h-10 py-2 px-4',
        sm: 'h-9 px-3 rounded-md',
        lg: 'h-11 px-8 rounded-md',
        icon: 'h-10 w-10',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);
```

### Pattern 3: Responsive Design
```tsx
// Mobile-first responsive design
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
  <Card className="w-full">
    <CardHeader>
      <CardTitle className="text-lg md:text-xl">Title</CardTitle>
    </CardHeader>
  </Card>
</div>
```

## Best Practices

### 1. Layer Order
- Tailwind utilities override Radix styles when needed
- Use `!important` sparingly
- Leverage CSS specificity instead of `!important`

### 2. Color System
- Use CSS variables for consistent theming
- Both Tailwind and Radix use the same color tokens
- Dark mode support built-in

### 3. Responsive Design
- Use Tailwind's responsive prefixes
- Leverage Radix's responsive props
- Test on actual devices

### 4. Performance
- Use Tailwind's JIT compiler
- Purge unused styles
- Optimize CSS bundle size

## Common Patterns

### Layout Components
```tsx
// Using Radix layout components with Tailwind
<Flex direction="column" gap="4" className="w-full max-w-4xl mx-auto">
  <Box className="p-6 bg-card rounded-lg">
    <Text size="4" className="font-semibold">Content</Text>
  </Box>
</Flex>
```

### Form Components
```tsx
<TextField.Root className="w-full">
  <TextField.Slot>
    <MagnifyingGlassIcon height="16" width="16" />
  </TextField.Slot>
  <TextField.Input 
    placeholder="Search..."
    className="placeholder:text-muted-foreground"
  />
</TextField.Root>
```

## Testing Integration

### Visual Regression Testing
```bash
# Run visual tests
npm run test:ui

# Check responsive design
npm run dev
# Open http://localhost:3000
```

### Accessibility Testing
- Both Tailwind and Radix provide ARIA attributes
- Test keyboard navigation
- Verify color contrast ratios

## Troubleshooting

### Issue: Styles not applying
- Check CSS import order
- Verify CSS variables are defined
- Check for conflicting styles

### Issue: Responsive styles not working
- Verify Tailwind breakpoints
- Check mobile-first approach
- Test on actual devices

### Issue: Dark mode not switching
- Verify CSS variables in .dark selector
- Check theme provider setup
- Test system preference detection

## Next Steps
1. Review the integration patterns above
2. Test with sample components
3. Create reusable component library
4. Document any project-specific patterns
5. Set up visual regression testing

## Resources
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Radix UI Themes Documentation](https://www.radix-ui.com/themes/docs/overview/getting-started)
- [CSS Variables Guide](https://developer.mozilla.org/en-US/docs/Web/CSS/Using_CSS_custom_properties)
